#!/usr/bin/env node

/**
 * Standalone script to fix invalid 'step' properties in datetime field metadata
 * that are causing validation errors in admin panel view configurations.
 */

const path = require('path');
const Database = require('better-sqlite3');

async function fixDatetimeMetadata() {
  console.log('Starting datetime step metadata cleanup...');

  // Path to the SQLite database
  const dbPath = path.join(__dirname, '..', '.tmp', 'data.db');
  
  let db;
  try {
    // Open the database
    db = new Database(dbPath);
    console.log('Connected to database:', dbPath);

    // Content types that have datetime fields with potential step issues
    const contentTypesToFix = [
      'api::course.course',
      'api::course-session.course-session', 
      'api::event.event',
      'api::event-session.event-session',
      'api::webinar.webinar'
    ];

    // Datetime fields that might have invalid step properties
    const datetimeFields = ['startDateTime', 'endDateTime'];

    // First, let's see what tables exist
    const tables = db.prepare(`
      SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%content_manager%'
    `).all();

    console.log('Available content manager tables:', tables.map(t => t.name));

    // Try different possible table names
    let tableName = null;
    const possibleTableNames = [
      'strapi_content_manager_configuration_content_types',
      'content_manager_configuration_content_types',
      'strapi_core_store_settings'
    ];

    for (const name of possibleTableNames) {
      try {
        const testQuery = db.prepare(`SELECT COUNT(*) as count FROM ${name} LIMIT 1`);
        testQuery.get();
        tableName = name;
        console.log(`Found table: ${tableName}`);
        break;
      } catch (e) {
        // Table doesn't exist, continue
      }
    }

    if (!tableName) {
      console.log('Could not find content manager configuration table');
      return;
    }

    // Query the configuration table
    let configurations;
    if (tableName === 'strapi_core_store_settings') {
      // Different structure for core store
      configurations = db.prepare(`
        SELECT id, key, value as configuration
        FROM strapi_core_store_settings
        WHERE key LIKE 'plugin_content_manager_configuration_content_types::%'
      `).all();

      // Extract uid from key and filter
      configurations = configurations.filter(config => {
        const uid = config.key.replace('plugin_content_manager_configuration_content_types::', '');
        return contentTypesToFix.includes(uid);
      }).map(config => ({
        ...config,
        uid: config.key.replace('plugin_content_manager_configuration_content_types::', '')
      }));
    } else {
      configurations = db.prepare(`
        SELECT id, uid, configuration
        FROM ${tableName}
        WHERE uid IN (${contentTypesToFix.map(() => '?').join(',')})
      `).all(...contentTypesToFix);
    }

    console.log(`Found ${configurations.length} content type configurations to check`);

    let totalUpdates = 0;

    for (const config of configurations) {
      let configData;
      let hasChanges = false;

      try {
        // Parse the configuration JSON
        configData = JSON.parse(config.configuration);
      } catch (error) {
        console.error(`Failed to parse configuration for ${config.uid}:`, error.message);
        continue;
      }

      // Check if metadatas exists
      if (!configData.metadatas) {
        console.log(`No metadatas found for ${config.uid}, skipping`);
        continue;
      }

      // Check each datetime field for invalid step properties
      for (const fieldName of datetimeFields) {
        if (configData.metadatas[fieldName] && 
            configData.metadatas[fieldName].edit && 
            configData.metadatas[fieldName].edit.step !== undefined) {
          
          console.log(`Removing invalid 'step' property from ${config.uid}.${fieldName}.edit`);
          delete configData.metadatas[fieldName].edit.step;
          hasChanges = true;
        }
      }

      // Update the configuration if changes were made
      if (hasChanges) {
        let updateStmt;
        if (tableName === 'strapi_core_store_settings') {
          updateStmt = db.prepare(`
            UPDATE strapi_core_store_settings
            SET value = ?
            WHERE id = ?
          `);
        } else {
          updateStmt = db.prepare(`
            UPDATE ${tableName}
            SET configuration = ?
            WHERE id = ?
          `);
        }

        updateStmt.run(JSON.stringify(configData), config.id);

        console.log(`✓ Updated configuration for ${config.uid}`);
        totalUpdates++;
      } else {
        console.log(`No step properties found in ${config.uid}, no changes needed`);
      }
    }

    console.log(`\nCompleted! Updated ${totalUpdates} content type configurations.`);

  } catch (error) {
    console.error('Error during datetime step metadata cleanup:', error);
    process.exit(1);
  } finally {
    if (db) {
      db.close();
      console.log('Database connection closed.');
    }
  }
}

// Run the script
fixDatetimeMetadata().catch(console.error);
