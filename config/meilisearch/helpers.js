const { MeiliSearch } = require('meilisearch')

const meilisearchClient = new MeiliSearch(
    { host: process.env.MEILISEARCH_HOST || 'http://localhost:7700',
      apiKey: process.env.MEILISEARCH_KEY || 'masterKey' })

const keysToMap = {
  'Curriculum': 'curriculum',
  'Related': 'related',
  'Facilitators': 'facilitators',
  'InfoBox': 'infoBoxes',
  'KeyTakeaways': 'keyTakeaways',
  'StudentTestimonial': 'studentTestimonials',
  'Partners': 'partners',
}

const contentTypeKeys = ['course', 'event', 'film', 'podcast', 'article', 'webinar']
const contentTypeMap = {
  courses: 'courses',
  events: 'clubs',
  films: 'films',
  podcasts: 'podcasts',
  articles: 'articles',
  webinars: 'webinars',
}

const includeContentType = (contentList) => {
  let cleanContentList = []
  for (let relatedObj of contentList) {
    for (let keyType of contentTypeKeys) {
      if (relatedObj.hasOwnProperty(keyType)) {
        // flatten the object
        cleanContentList.push({ ...relatedObj[keyType], contentType: contentTypeMap[`${keyType}s`] })
        break
      }
    }
  }
  return cleanContentList
}

const cleanEntryProperties = (entry) => {
  let mappedEntry = []
  for (let [key, newKey] of Object.entries(keysToMap)) {
    if (entry[key] !== undefined) {
      mappedEntry[newKey] = entry[key]
      delete mappedEntry[key]
    }
  }
  const keysToKeep = Object.keys(entry).filter(key => {
    return !mappedEntry.hasOwnProperty(key) && !keysToMap.hasOwnProperty(key)
  })
  for (let key of keysToKeep) mappedEntry[key] = entry[key]

  return mappedEntry
}

const mapTeachers = (teachersComponent) => {
  return teachersComponent && teachersComponent.teachers? teachersComponent.teachers.map(teacher => {
    return {
      id: teacher.id,
      name: teacher.name,
      cardTitle: teacher.cardTitle,
      slug: teacher.slug,
      thumbnail: teacher.picture,
      picture: teacher.cover,
      description: teacher.description,
      coursesTotal: teacher.relatedCourses?.length,
    }
  }) : []
}

const mapSessionsMeta = (sessionsMeta) => {
  let teacherTitle = null
  let teacherDescription = null
  let teacherList = []

  let taskTitle = null
  let taskDescription = null
  let taskList = []

  let transcriptTitle = null
  let transcriptDescription = null
  let transcriptList = []

  let fileTitle = null
  let fileDescription = null
  let fileList = []

  sessionsMeta.map(meta => {
    // gather all teachers
    if (meta['__component'].includes("speaker-list")) {
      teacherTitle = meta.title
      teacherDescription = meta.description
      teacherList.push(...meta.teachers)
    }
    // gather all tasks
    if (meta['__component'].includes("task-list")) {
      taskTitle = meta.title
      taskDescription = meta.description
      taskList.push(...meta.tasks)
    }
    // gather all transcripts
    if (meta['__component'].includes("transcript-list")) {
      transcriptTitle = meta.title
      transcriptDescription = meta.description
      transcriptList.push(...meta.transcripts)
    }
    // gather all files
    if (meta['__component'].includes("file-list")) {
      fileTitle = meta.title
      fileDescription = meta.description
      fileList.push(...meta.files)
    }
  })

  return {
    teachers: {
      title: teacherTitle,
      description: teacherDescription,
      list: [...new Set(teacherList)],
    },
    tasks: {
      title: taskTitle,
      description: taskDescription,
      list: [...new Set(taskList)],
    },
    transcripts: {
      title: transcriptTitle,
      description: transcriptDescription,
      list: [...new Set(transcriptList)],
    },
    files: {
      title: fileTitle,
      description: fileDescription,
      list: [...new Set(fileList)],
    },
  }
}

const mapSessionResourcesComponent = (section) => {
  let cleanSection = []

  section.map(component => {
    if (component['__component'].includes("file-element")) {
      cleanSection.push({
        title: component.title,
        description: component.description,
        link: component.attachment?.url?
          (!component.attachment?.url?.startsWith('http')?
            `https://${component.attachment.url}` : component.attachment.url) : null,
        filename: component.attachment?.name,
        type: 'file',
      })
    } else if (component['__component'].includes("hyperlink-element")) {
      cleanSection.push({
        title: component.title,
        link: component.link,
        type: 'hyperlink',
      })
    } else if (component['__component'].includes("text-element")) {
      cleanSection.push({
        content: component.content,
        type: 'text',
      })
    }
  })

  return cleanSection
}

const mapHeader = (header) => {
  if (!header) {
    return {
      id: '',
      title: '',
      link: {
        text: '',
        url: '',
      }
    }
  }

  return {
    id: header.id,
    title: header.title,
    link: {
      text: header.primaryPublicCTA,
      url: header.link,
    }
  }
}

const cleanCategories = (categories) => {
  if (!categories || !Array.isArray(categories)) {
    return [];
  }

  let cleanedCategories = []
  categories.map(category => {
    if (category) {
      cleanedCategories.push({
        id: category.id,
        title: category.title,
        slug: category.slug,
        hubspot_list_id: category.hubspot_list_id,
      })
    }
  })
  return cleanedCategories
}

const cleanUpImageBuffer = (imgObj) => {
  // remove buffer property from image fields
  if (!imgObj || !imgObj.formats) return {}

  let cleanImgObj = Object.assign({}, imgObj)
  cleanImgObj.formats = {}

  for (let format of Object.keys(imgObj.formats)) {
    cleanImgObj.formats[format] = imgObj.formats[format]
    delete imgObj.formats[format].buffer
  }

  return cleanImgObj
}

const mapSEO = (data) => {
  if (!data) {
    return {
      metaTitle: null,
      metaDescription: null,
      metaImage: null,
      keywords: null,
      metaRobots: null,
      structuredData: null,
      metaViewport: null,
      canonicalURL: null,
      metaSocial: [],
    };
  }

  // auto-populate SEO fields
  const metaTitle = (data.title && data.title.length > 60)?
    `${data.title.substr(0, 57)}...` : data.title || null
  const metaDescription = (data.description && data.description.length > 160)?
    `${data.description.substr(0, 157)}...` : data.description || null
  return  {
    metaTitle,
    metaDescription,
    metaImage: data.thumbnail || data.picture,
    keywords: null,
    metaRobots: null,
    structuredData: null,
    metaViewport: null,
    canonicalURL: null,
    metaSocial: [],
  }
}

const handleSEOmapping = (data) => {
  const seoData = data.seo || mapSEO(data)
  seoData.keywords = seoData.keywords?
     seoData.keywords.replace(/["\t\r]/g, "").replace(/[\n\r]/g, ", ").trim() : null
  return seoData
}

const cleanDuplicates = (duplicateArray) => {
  return duplicateArray.filter((obj, index, self) => {
    return index === self.findIndex(t => (t.id === obj.id))
  })
}

const mapEventSessions = (e, contentType) => {
  if (!e || !e.eventSession) {
    return null;
  }

  return {
    id: e.eventSession.id,
    title: e.eventSession.title,
    // description: e.eventSession.description,
    startDateTime: e.eventSession.startDateTime,
    endDateTime: e.eventSession.endDateTime,
    slug: e.eventSession.slug,
    seriesSlug: e.eventSession.event?.slug,
    seriesTitle: e.eventSession.event?.title,
    directLink: `${contentType}/${e.eventSession.event?.slug}/${e.eventSession.slug}`,
    thumbnail: e.eventSession.thumbnail,
    picture: e.eventSession.picture,
    teachers: e.eventSession.teachersList?.teachers,
    contentType,
    // event: {
    //   id: e.eventSession.event.id,
    //   title: e.eventSession.event.title,
    //   headline: e.eventSession.event.headline,
    //   dateHeadline: e.eventSession.event.dateHeadline,
    //   startDateTime: e.eventSession.event.startDateTime,
    //   endDateTime: e.eventSession.event.endDateTime,
    //   slug: e.eventSession.event.slug,
    //   thumbnail: e.eventSession.event.thumbnail,
    //   picture: e.eventSession.event.picture,
    //   teachers: mapTeachers(e.eventSession.event.teachersList),
    //   contentType: contentType,
    // }
  }
}

const fetchTeachersRelatedContent = async (teacher) => {
  const teachersListSlugQuery = `TeacherList.teachers.slug = "${teacher.slug}"`
  const teachersListNameQuery = `TeacherList.teachers.name = "${teacher.name}"`
  const mediaTeachersListQuery = `${teachersListSlugQuery} AND ${teachersListNameQuery}`
  const TeacherListSlugQuery = `teachersList.teachers.slug = "${teacher.slug}"`
  const TeacherListNameQuery = `teachersList.teachers.name = "${teacher.name}"`
  const eventSessionsTeacherListQuery = `${TeacherListSlugQuery} AND ${TeacherListNameQuery}`
  const teachersSlugQuery = `teachers.slug = "${teacher.slug}"`
  const teachersNameQuery = `teachers.name = "${teacher.name}"`
  const teachersQuery = `${teachersSlugQuery} AND ${teachersNameQuery}`

  const courses = await meilisearchClient.index('courses').search('', {
    filter: [teachersQuery]
  })
  const eventsPrivate = await meilisearchClient.index('sessions').search('', {
    filter: [teachersQuery]
  })
  // clean up private properties from event sessions
  const events = (!!eventsPrivate && eventsPrivate.hits && eventsPrivate.hits.length > 0)?
      eventsPrivate.hits.map(e => {
        delete e.videoURL
        delete e.audioURL
        delete e.liveURL
        delete e.readingResources
        delete e.practices
        e.teachers = e.teachersList?.teachers || []
        e.teachersTitle = e.teachersList?.title || null
        delete e.teachersList

        return e
      }) : eventsPrivate
  const articles = await meilisearchClient.index('articles').search('', {
    filter: [teachersQuery]
  })
  const films = await meilisearchClient.index('films').search('', {
    filter: [teachersQuery]
  })
  const podcasts = await meilisearchClient.index('podcasts').search('', {
    filter: [teachersQuery]
  })

  const related = {}

  for (let [contentType, results] of Object.entries(
      { courses, events, articles, films, podcasts })) {
    if (!!results && !!results.hits && results.hits.length > 0)
      related[contentType] = results.hits
  }

  // if (Object.values(related).length > 0)
  //   console.log(related)

  return related
}

const mapRelatedContent = (contentObjs) => {
  const courses = contentObjs.relatedCourses
  const events = contentObjs.relatedEvents
  const articles = contentObjs.relatedArticles
  const films = contentObjs.relatedFilms
  const podcasts = contentObjs.relatedPodcasts

  const mappedContent = {}

  for (let [contentType, results] of Object.entries({ ...contentObjs })) {
    if (!!results && results.length > 0) {
      const publishedResults = results.filter(r => r.publishedAt)
      mappedContent[contentType] = publishedResults.map(r => ({ ...r, contentType }))
    }
  }

  return mappedContent
}

const aggregateModuleTeachers = (modules) => {
  let teacherList = []
  modules?.map(module => {
    module.thumbnail = module.sessions[0]?.thumbnail
    module.teachers = []
    module.sessions?.map(session => {
      // gather all speakers
      session.teachersList?.teachers.map(teacher => {
        const teacherObj = {
          id: teacher.id,
          name: teacher.name,
          // description: teacher.description,
          // bio: teacher.bio,
          slug: teacher.slug,
          thumbnail: teacher.picture,
          picture: teacher.cover,
          coursesTotal: teacher.relatedCourses?.length,
        }
        teacherList.push(teacherObj)
      })
    })
  })
  return cleanDuplicates(teacherList)
}

const cleanCategoryGroup = (categoryGroup) => {
  if (!categoryGroup) {
    return null;
  }

  return {
    title: categoryGroup.title,
    slug: categoryGroup.slug,
    color: categoryGroup.colour,
    query: categoryGroup.categories?.map(category => category.slug).join(' OR ') || '',
  }
}

const getCategoryGroup = async (strapi, category) => {
  return await strapi.entityService.findMany('api::category-groups.category-groups', {
    filters: {
      categories: {
        id: category.id
      }
    },
    populate: {
      categories: true,
    }
  }).then((categoryGroups) => {
    return cleanCategoryGroup(categoryGroups[0])
  })
}

const isoDateToTimestamp = (isoDate) => {
  return Math.floor(new Date(isoDate).getTime() / 1000)
}

const cleanMagazineFeatured = (featuredObj, includeDescription = false) => {
  if (!featuredObj) {
    return null;
  }

  let cleanObj = {
    title: featuredObj.title,
    slug: featuredObj.slug,
    // picture: featuredObj.picture,
    teachers: featuredObj.TeacherList?.teachers?.map(teacher => {
      return {
        name: teacher.name,
        cardTitle: teacher.cardTitle,
        slug: teacher.slug,
        picture: teacher.picture,
      }
    }) || [],
    cardTitle: featuredObj.cardTitle,
  }
  if (includeDescription) cleanObj.description = featuredObj.description
  return cleanObj
}

const mapRelatedSession = (relatedSession) => {
  if (!relatedSession || !relatedSession.length) {
    return null;
  }

  const contentType = relatedSession[0].hasOwnProperty('webinar')?
    'webinars' : 'q-and-a'
  let session = contentType === 'webinars'?
    relatedSession[0].webinar : relatedSession[0].q_and_a
  const directLink = contentType === 'webinars'?
      `webinars/${session.slug}/` :
      `courses/${session.course?.slug}/${session.slug}/`

  const teachersList = session.TeacherList?.teachers || session.TeachersList?.teachers || []
  const teachers = teachersList.map(teacher => {
    return {
      id: teacher.id,
      name: teacher.name,
      cardTitle: teacher.cardTitle || teacher.name,
      slug: teacher.slug,
      thumbnail: teacher.picture,
      picture: teacher.cover,
      directLink: `faculty/${teacher.slug}/`,
    }
  })

  for (let prop of [
    'TeacherList', 'TeachersList', 'videoURL', 'about', 'course', 'zoomURL',
    'zoomMeetingNumber', 'zoomMeetingPassword', 'createdAt', 'updatedAt',
    'publishedAt', 'related', 'liveURL', 'audioURL', 'readingResources',
    'moduleTitle', 'moduleIndex', 'chapterIndexInModule',])
    delete session[prop]

  return {
    ...session,
    startDateTimeTimestamp: isoDateToTimestamp(session.startDateTime),
    endDateTimeTimestamp: isoDateToTimestamp(session.endDateTime),
    directLink,
    teachers,
    contentType,
  }

}

module.exports = {
  aggregateModuleTeachers,
  cleanEntryProperties,
  cleanDuplicates,
  cleanCategories,
  cleanMagazineFeatured,
  cleanUpImageBuffer,
  includeContentType,
  fetchTeachersRelatedContent,
  getCategoryGroup,
  mapEventSessions,
  mapRelatedContent,
  handleSEOmapping,
  isoDateToTimestamp,
  mapHeader,
  mapSessionResourcesComponent,
  mapSessionsMeta,
  mapRelatedSession,
  mapTeachers,
}
