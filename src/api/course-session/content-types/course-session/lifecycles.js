module.exports = {
  async beforeCreate(event) {
    if (!event.params.data.cardTitle && event.params.data.title) {
      event.params.data.cardTitle = event.params.data.title;
    }
    if (event.params.data['q-and-a'] === true &&
      !event.params.data.discussionId && event.params.data.title && event.params.data.slug) {
        event.params.data.discussionId = await strapi.service('api::course.course').createDiscussion(
          'q-and-a', event.params.data.slug, event.params.data.title)
    }
  },
  async beforeUpdate(event) {
    const session = await strapi.entityService.findOne(
        'api::course-session.course-session', event.params.where.id, {})

    if (!event.params.data.cardTitle && (event.params.data.title || session.title)) {
      event.params.data.cardTitle = event.params.data.title || session.title;
    }
    if ((event.params.data['q-and-a'] === true || session['q-and-a'] === true) &&
        (event.params.data.title || session.title) &&
        (event.params.data.slug || session.slug)) {
        event.params.data.discussionId = await strapi.service('api::course.course').createDiscussion(
          'q-and-a',
          event.params.data.slug || session.slug,
          event.params.data.title || session.title)
    }
  },
  async afterUpdate(event) {
    const session = await strapi.entityService.findOne(
        'api::course-session.course-session', event.result.id, {
          populate: { course: { populate: { thumbnail: true } } }
        })
    // TODO: check if teacher was removed and update it as well
    if (event.result?.teachersList?.teachers) {
      // update entities related content
      for (let t of event.result.teachersList.teachers) {
        const teacher = await strapi.entityService.findOne('api::entity.entity', t.id, {
          populate: { relatedCourses: true }
        })
        if (teacher.relatedCourses.some(c => c.id === session.course.id)) {
          // console.log(`course-session afterUpdate(): teacher ${t.name} [${t.id}] already has 'relatedCourses' ${session.course.title} [${session.course.id}]`)
          continue
        }
        try {
          await strapi.entityService.update('api::entity.entity', teacher.id, { data: {
              relatedCourses: [...teacher.relatedCourses, session.course]
            } });
        } catch (e) {
          console.error(`course-session afterUpdate(): error updating teacher ${t.name} [${t.id}] relatedCourses: ${e.message}`)
        }
      }
    }
    // Skip updatePages() and cache invalidation during bulk updates to prevent cascading
    const collectionsService = strapi.plugin('update-collections-trigger').service('collections');
    if (!collectionsService.isBulkUpdateInProgress()) {
      await strapi.plugin('deployment-trigger').service('deployment').updatePages();
      collectionsService.invalidateCache('course-sessions', session.event?.slug, event.result?.slug || session.slug);
    }
  }
};
