'use strict';

/**
 * Migration to fix invalid 'step' properties in datetime field metadata
 * that are causing validation errors in admin panel view configurations.
 * 
 * The error occurs when trying to configure admin panel views for content types
 * with datetime fields that have invalid 'step' properties in their metadata.
 */

async function up(trx) {
  console.log('Starting datetime step metadata cleanup migration...');

  try {
    // Content types that have datetime fields with potential step issues
    const contentTypesToFix = [
      'api::course.course',
      'api::course-session.course-session', 
      'api::event.event',
      'api::event-session.event-session',
      'api::webinar.webinar'
    ];

    // Datetime fields that might have invalid step properties
    const datetimeFields = ['startDateTime', 'endDateTime'];

    // Query the strapi_content_manager_configuration_content_types table
    // This table stores admin panel view configurations including field metadata
    const configurations = await trx('strapi_content_manager_configuration_content_types')
      .whereIn('uid', contentTypesToFix);

    console.log(`Found ${configurations.length} content type configurations to check`);

    for (const config of configurations) {
      let configData;
      let hasChanges = false;

      try {
        // Parse the configuration JSON
        configData = JSON.parse(config.configuration);
      } catch (error) {
        console.error(`Failed to parse configuration for ${config.uid}:`, error.message);
        continue;
      }

      // Check if metadatas exists
      if (!configData.metadatas) {
        console.log(`No metadatas found for ${config.uid}, skipping`);
        continue;
      }

      // Check each datetime field for invalid step properties
      for (const fieldName of datetimeFields) {
        if (configData.metadatas[fieldName] && 
            configData.metadatas[fieldName].edit && 
            configData.metadatas[fieldName].edit.step !== undefined) {
          
          console.log(`Removing invalid 'step' property from ${config.uid}.${fieldName}.edit`);
          delete configData.metadatas[fieldName].edit.step;
          hasChanges = true;
        }
      }

      // Update the configuration if changes were made
      if (hasChanges) {
        await trx('strapi_content_manager_configuration_content_types')
          .where('id', config.id)
          .update({
            configuration: JSON.stringify(configData)
          });
        
        console.log(`Updated configuration for ${config.uid}`);
      } else {
        console.log(`No step properties found in ${config.uid}, no changes needed`);
      }
    }

    console.log('Datetime step metadata cleanup migration completed successfully');

  } catch (error) {
    console.error('Error during datetime step metadata cleanup migration:', error);
    throw error;
  }
}

async function down(trx) {
  // This migration removes invalid data, so there's no meaningful rollback
  console.log('Rollback not applicable for datetime step metadata cleanup migration');
}

module.exports = { up, down };
